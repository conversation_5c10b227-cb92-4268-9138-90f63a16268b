import React, { useEffect, useRef, useState } from 'react';
import { But<PERSON>, Row, Col, Table, Space, Breadcrumb, message, TablePaginationConfig } from 'antd';
import moment from 'moment';
import { SharkRForm } from '@sharkr/form';
import { openUrl } from '@sharkr/utils'
import { getBasePictureList } from '../../../../services/pictureService';
import {
    BasePicQueryVO,
    BasePictureListBO,
    BasePictureStatisticsBO,
    IAntDPaginationProps,
    IPictureQueryProps,
    TEAM_ID
} from '../../../../interfaces';
import { PictureUploadModal } from '../../components/pictureUploadModal';
import { PicturePrimaryListModal } from '../../components';
import { useHistory } from 'react-router-dom';
import { AppConfig } from '@shark/core';

export const PictureBaseList: React.FC = () => {
    const [uploadModalVisible, setUploadModalVisible] = useState(false);
    const searchRef = useRef<any>();
    const history = useHistory()

    const [searchParam, setSearchParam] = useState<IPictureQueryProps>({
        pageNo: 1,
        pageSize: 10,
        channelProductId: undefined,
        itemName: undefined,
        operator: undefined,
        secondBuIds: undefined,
        spuId: undefined,
        startTime: undefined,
        endTime: undefined
    });

    const [dataSource, setDataSource] = useState<BasePictureListBO[]>([]);
    const [pagination, setPagination] = useState<IAntDPaginationProps>({
        current: 1,
        pageSize: 10,
        total: 0
    });
    const [loading, setLoading] = useState(false);
    const [primaryPicModalParams, setPrimaryPicModalParams] = useState<{
      teamId: TEAM_ID,
      spuId: number,
      spuName: string,
    }>()

    // 搜索表单配置
    const searchSchema: any = [
        {
            key: 'spuId',
            type: 'Input',
            label: '严选商品ID',
            props: {
                placeholder: '请输入商品ID',
                allowClear: true
            }
        },
        {
            key: 'itemName',
            type: 'Input',
            label: '严选商品名称',
            props: {
                placeholder: '请输入商品名称',
                allowClear: true
            }
        },
        {
          key: 'secondBuId',
          type: 'Select',
          label: '归属BU',
          props: {
              placeholder: '请输入二级BU ID',
              allowClear: true
          }
        },
        {
            key: 'channelProductId',
            type: 'Input',
            label: '渠道商品ID',
            props: {
                placeholder: '请输入渠道商品ID',
                allowClear: true
            }
        },
        {
          key: 'operator',
          type: 'IcacSingleUserSelect',
          label: '操作人',
          props: {
              placeholder: '请输入操作人',
              allowClear: true
          }
        },
        {
            key: 'timeRange',
            type: 'RangePicker',
            label: '操作时间',
            props: {
                placeholder: ['开始时间', '结束时间'],
                showTime: true,
                format: 'YYYY-MM-DD HH:mm:ss',
                style: { width: '350px' }
            }
        }
    ];

    // 表格列定义
    const columns: any = [
        {
            title: '严选商品ID',
            dataIndex: 'spuId',
            key: 'spuId'
        },
        {
            title: '严选商品名称',
            dataIndex: 'name',
            key: 'name'
        },
        {
            title: '归属BU',
            key: 'buInfo',
            render: (record: BasePictureListBO) => `${record.buName}-${record.secondBuName}`
        },
        {
            title: '主图信息',
            dataIndex: 'pictureStatisticsList',
            key: 'pictureStatisticsList',
            render: (picList: BasePictureStatisticsBO[], record: BasePictureListBO) => (
              <Space direction="vertical" size="small">
                {picList.map((pic: BasePictureStatisticsBO, index: number) => (
                  <Button size="small" type="link" key={pic.teamId} onClick={() => openPrimaryPicModal(pic.teamId, record.spuId, record.name)}>{`${pic.teamName}：${pic.totalCount}张`}</Button>
                ))}
              </Space>
            )
        },
        {
            title: 'SKU透底图',
            dataIndex: 'skuPicCount',
            key: 'skuPicCount',
            render: (count: number) => (
              <Button size="small" type="link">
                {`${count}张`}
              </Button>
            )
        },
        {
            title: '操作人',
            dataIndex: 'updator',
            key: 'updator'
        },
        {
            title: '最近更新时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            render: (time: number) => moment(time).format('YYYY-MM-DD HH:mm:ss')
        },
        {
            title: '操作',
            key: 'action',
            width: 120,
            render: (record: BasePictureListBO) => (
                <Space>
                    <Button type="link" size="small" onClick={() => handleViewDetail(record.spuId)}>
                        查看图片详情
                    </Button>
                </Space>
            )
        }
    ];

    // 获取列表数据
    const getList = async () => {
        setLoading(true);
        try {
            const queryParams: BasePicQueryVO = {
                channelProductId: searchParam.channelProductId,
                endTime: searchParam.endTime,
                itemName: searchParam.itemName,
                operator: searchParam.operator,
                secondBuIds: searchParam.secondBuIds || [],
                spuId: searchParam.spuId,
                startTime: searchParam.startTime
            };

            const res = await getBasePictureList(queryParams);
            if (res && res.code === 200) {
                const { result, paginationVO } = res.data || { result: [], paginationVO: {} };
                const antdPagination: IAntDPaginationProps = {
                    current: paginationVO.page,
                    pageSize: paginationVO.size,
                    total: paginationVO.total
                };
                setPagination(antdPagination);
                setDataSource(result);
            } else {
                message.error('获取底图列表失败');
            }
        } catch (error) {
            message.error('获取底图列表失败');
        } finally {
            setLoading(false);
        }
    };

    // 搜索
    const onSearch = async () => {
        const formValues = await searchRef.current?.submit();
        const newSearchParam: IPictureQueryProps = {
            ...searchParam,
            pageNo: 1,
            ...formValues
        };

        // 处理时间范围
        if (formValues.timeRange && formValues.timeRange.length === 2) {
            newSearchParam.startTime = formValues.timeRange[0].valueOf();
            newSearchParam.endTime = formValues.timeRange[1].valueOf();
        }

        setSearchParam(newSearchParam);
    };

    // 重置搜索
    const onReset = () => {
        searchRef.current?.reset();
        const resetParam: IPictureQueryProps = {
            pageNo: 1,
            pageSize: 10,
            channelProductId: undefined,
            itemName: undefined,
            operator: undefined,
            secondBuIds: [],
            spuId: undefined,
            startTime: undefined,
            endTime: undefined
        };
        setSearchParam(resetParam);
    };

    // 分页变化
    const handlePaginationChange = (pagination: TablePaginationConfig) => {
        const newSearchParam: IPictureQueryProps = {
            ...searchParam,
            pageNo: pagination.current || 1,
            pageSize: pagination.pageSize || searchParam.pageSize
        };
        setSearchParam(newSearchParam);
    };

    // 打开主图弹窗
    const openPrimaryPicModal = (teamId: TEAM_ID, spuId: number, spuName: string) => {
      setPrimaryPicModalParams({
        teamId,
        spuId,
        spuName
      })
    }

    // 跳转详情页
    const handleViewDetail = (spuId: number) => {
      // history.push(`/picture/base/detail/${spuId}`);
      openUrl(`${AppConfig.contextPath}/#/picture/base/detail/${spuId}`);
    }

    useEffect(() => {
        getList();
    }, [searchParam]);

    return (
        <>
            <Breadcrumb separator=">" style={{ marginBottom: 10 }}>
                <Breadcrumb.Item>图片管理</Breadcrumb.Item>
                <Breadcrumb.Item>底图列表</Breadcrumb.Item>
            </Breadcrumb>
            <section className="sharkr-section picture-base-list">
                <div className="sharkr-section-header with-tools">
                    <span className="sharkr-section-header-title">商品底图库</span>
                    <span className="sharkr-section-header-sub-title">{`（共${dataSource.length}条）`}</span>
                    <div className="sharkr-tools">
                        <Button type="primary" onClick={() => setUploadModalVisible(true)}>上传图片</Button>
                    </div>
                </div>
                <div className="sharkr-section-content">
                    <SharkRForm
                        className="sharkr-form-inline searchForm"
                        ref={searchRef}
                        schema={searchSchema}
                        {...{
                            labelCol: { span: 6 },
                            wrapperCol: { span: 18 }
                        }}
                    />
                    <Row>
                        <Col span={8} xl={8} xxl={6}>
                            <Row>
                                <Col offset={5}>
                                    <Space>
                                        <Button type="primary" onClick={onSearch}>
                                            搜索
                                        </Button>
                                        <Button onClick={onReset}>
                                            重置
                                        </Button>
                                    </Space>
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <Table
                        className="sharkr-table margin-t-4x"
                        columns={columns}
                        dataSource={dataSource}
                        key="pictureBaseListTable"
                        pagination={pagination}
                        onChange={handlePaginationChange}
                        rowKey="spuId"
                        loading={loading}
                        scroll={{ x: 800 }}
                    />
                </div>
            </section>
            <PictureUploadModal
                isOpen={uploadModalVisible}
                onCancel={() => setUploadModalVisible(false)}
                onOk={() => {
                    setUploadModalVisible(false);
                    getList();
                }}
            />
            <PicturePrimaryListModal
              isOpen={!!primaryPicModalParams}
              onCancel={() => setPrimaryPicModalParams(undefined)}
              {...primaryPicModalParams}
            />
        </>
    );
};
